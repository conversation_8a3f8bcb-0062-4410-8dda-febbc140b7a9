#!/bin/bash

# New-API 综合管理脚本
# 功能：统一管理 New-API 的所有操作
# 版本：v3.0 - Enhanced Edition
# 作者：AI Assistant
# 更新：增强重建、智能代码管理、定时备份、改进交互

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${CYAN}${BOLD}$1${NC}"; }

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SH_DIR="$SCRIPT_DIR/sh"
NEW_API_DIR="/root/workspace/new-api"
CRON_BACKUP_SCRIPT="/root/workspace/new-api/sh/auto-backup.sh"

# 检查服务运行状态
check_service_status() {
    local service_running=false

    # 检查 New-API 容器
    if docker ps --format "table {{.Names}}" | grep -q "new-api"; then
        service_running=true
    fi

    # 检查 MySQL 容器
    if docker ps --format "table {{.Names}}" | grep -q "mysql"; then
        service_running=true
    fi

    echo "$service_running"
}

# 智能停止服务（仅在运行时停止）
smart_stop_services() {
    local is_running=$(check_service_status)

    if [ "$is_running" = "true" ]; then
        log_info "检测到服务正在运行，正在停止..."
        "$SH_DIR/service.sh" stop
        log_success "服务已停止"
        return 0
    else
        log_info "服务未运行，跳过停止步骤"
        return 1
    fi
}

# 检查脚本目录
check_scripts() {
    if [ ! -d "$SH_DIR" ]; then
        log_error "脚本目录不存在: $SH_DIR"
        return 1
    fi

    local required_scripts=("service.sh" "database.sh")
    local missing_scripts=()

    for script in "${required_scripts[@]}"; do
        if [ ! -f "$SH_DIR/$script" ]; then
            missing_scripts+=("$script")
        fi
    done

    if [ ${#missing_scripts[@]} -gt 0 ]; then
        log_error "缺少脚本文件: ${missing_scripts[*]}"
        return 1
    fi

    # 确保脚本可执行
    chmod +x "$SH_DIR"/*.sh

    return 0
}

# 显示主菜单
show_main_menu() {
    clear
    log_header "========================================"
    log_header "        New-API 综合管理系统"
    log_header "========================================"
    echo
    echo -e "${CYAN}服务管理:${NC}"
    echo "  1) 启动服务          - 启动 New-API 服务"
    echo "  2) 停止服务          - 停止 New-API 服务"
    echo "  3) 重启服务          - 重启 New-API 服务"
    echo "  4) 服务状态          - 查看服务运行状态"
    echo "  5) 服务管理菜单      - 进入详细服务管理"
    echo
    echo -e "${CYAN}代码管理:${NC}"
    echo "  6) 仓库状态总览      - 显示所有仓库状态和更新情况"
    echo "  7) 从公有库拉取      - 从官方公有库获取最新代码"
    echo "  8) 推送到私有库      - 推送代码到您的私有仓库"
    echo
    echo -e "${CYAN}数据库管理:${NC}"
    echo "  9) 备份数据库        - 创建数据库备份文件"
    echo "  10) 恢复数据库       - 进入数据库管理详细菜单"
    echo "  11) 数据库维护       - 优化表、分析性能、显示统计"
    echo "  12) 定时备份设置     - 配置自动备份计划任务"
    echo
    echo -e "${CYAN}其他操作:${NC}"
    echo "  13) 显示帮助         - 查看详细使用说明"
    echo -e "  ${YELLOW}[回车] 退出${NC}          - 退出管理系统"
    echo
    echo "========================================"
}

# 服务管理菜单
service_menu() {
    while true; do
        clear
        log_header "========================================"
        log_header "           服务管理菜单"
        log_header "========================================"
        echo
        echo "1) 启动服务          - 启动 New-API 和 Nginx 容器"
        echo "2) 停止服务          - 停止所有相关容器"
        echo "3) 重启服务          - 重启所有服务"
        echo "4) 重新构建          - 停止→清理缓存→重建→启动"
        echo "5) 服务状态          - 容器状态+连通性+资源使用"
        echo "6) 查看日志          - 查看指定服务的历史日志"
        echo "7) 跟踪日志          - 实时跟踪服务日志输出"
        echo "8) 进入容器          - 进入指定容器的命令行环境"
        echo "0) 返回主菜单"
        echo
        read -p "请选择操作 [0-8]: " choice
        
        case $choice in
            1) "$SH_DIR/service.sh" start; pause ;;
            2) "$SH_DIR/service.sh" stop; pause ;;
            3) "$SH_DIR/service.sh" restart; pause ;;
            4) "$SH_DIR/service.sh" rebuild; pause ;;
            5) "$SH_DIR/service.sh" status; pause ;;
            6) 
                echo "选择服务: 1) new-api  2) nginx  3) all"
                read -p "请选择 [1-3]: " svc_choice
                case $svc_choice in
                    1) "$SH_DIR/service.sh" logs new-api ;;
                    2) "$SH_DIR/service.sh" logs nginx ;;
                    3) "$SH_DIR/service.sh" logs all ;;
                    *) log_error "无效选择" ;;
                esac
                pause
                ;;
            7)
                echo "选择服务: 1) new-api  2) nginx"
                read -p "请选择 [1-2]: " svc_choice
                case $svc_choice in
                    1) "$SH_DIR/service.sh" follow new-api ;;
                    2) "$SH_DIR/service.sh" follow nginx ;;
                    *) log_error "无效选择" ;;
                esac
                ;;
            8)
                echo "选择容器: 1) new-api  2) nginx  3) mysql"
                read -p "请选择 [1-3]: " svc_choice
                case $svc_choice in
                    1) "$SH_DIR/service.sh" enter new-api ;;
                    2) "$SH_DIR/service.sh" enter nginx ;;
                    3) "$SH_DIR/service.sh" enter mysql ;;
                    *) log_error "无效选择" ;;
                esac
                ;;
            0) break ;;
            *) log_error "无效选择，请重新输入"; sleep 2 ;;
        esac
    done
}

# 数据库管理菜单
database_menu() {
    while true; do
        clear
        log_header "========================================"
        log_header "          数据库管理菜单"
        log_header "========================================"
        echo
        echo "1) 检查数据库连接    - 测试MySQL容器连接状态"
        echo "2) 备份数据库        - 创建完整的数据库备份文件"
        echo "3) 恢复数据库        - 从备份文件恢复数据库"
        echo "4) 数据库维护        - 优化表+分析+显示统计信息"
        echo "5) 显示备份列表      - 查看所有可用的备份文件"
        echo "6) 清理旧备份        - 删除指定天数前的备份文件"
        echo "7) 定时备份设置      - 配置cron自动备份任务"
        echo "8) 查看备份日志      - 查看自动备份的执行日志"
        echo "0) 返回主菜单"
        echo
        read -p "请选择操作 [0-8]: " choice
        
        case $choice in
            1) "$SH_DIR/database.sh" check; pause ;;
            2) 
                read -p "输入备份名称（可选，回车使用时间戳）: " backup_name
                "$SH_DIR/database.sh" backup "$backup_name"
                pause
                ;;
            3)
                "$SH_DIR/database.sh" list
                echo
                read -p "输入备份文件完整路径: " backup_file
                if [ -n "$backup_file" ]; then
                    "$SH_DIR/database.sh" restore "$backup_file"
                fi
                pause
                ;;
            4) "$SH_DIR/database.sh" maintain; pause ;;
            5) "$SH_DIR/database.sh" list; pause ;;
            6)
                read -p "输入要保留的天数（默认30天）: " days
                "$SH_DIR/database.sh" cleanup "${days:-30}"
                pause
                ;;
            7) setup_auto_backup; pause ;;
            8)
                if [ -f "/root/workspace/new-api/auto-backup.log" ]; then
                    echo "=== 自动备份日志 ==="
                    tail -50 "/root/workspace/new-api/auto-backup.log"
                else
                    log_info "暂无自动备份日志"
                fi
                pause
                ;;
            0) break ;;
            *) log_error "无效选择，请重新输入"; sleep 2 ;;
        esac
    done
}

# 代码管理菜单
code_menu() {
    while true; do
        clear
        log_header "========================================"
        log_header "           代码管理菜单"
        log_header "========================================"
        echo
        echo "1) 检查Git状态       - 查看当前分支、提交和未保存更改"
        echo "2) 仓库状态总览      - 显示所有仓库状态和更新情况"
        echo "3) 拉取最新代码      - 从当前远程仓库获取最新更新"
        echo "4) 从公有库拉取      - 从官方公有库获取最新代码"
        echo "5) 推送到私有库      - 推送代码到您的私有仓库"
        echo "6) 应用子路径补丁    - 修复/ai路径相关的静态资源问题"
        echo "7) 应用API端点补丁   - 修复API端点路径问题"
        echo "8) Gemini端点修复    - 修复Gemini API模型列表端点"
        echo "9) 验证配置          - 检查所有补丁是否正确应用"
        echo "10) 完整更新流程     - 拉取+补丁+验证 (不含重建)"
        echo "11) 快速修复         - 解决404、重定向、CSP等问题"
        echo "12) 自动更新（完整流程）- 最完整的更新 (备份至/root/workspace/backup/+重建)"
        echo "0) 返回主菜单"
        echo
        read -p "请选择操作 [0-12]: " choice

        case $choice in
            1) "$SH_DIR/update.sh" check; pause ;;
            2) "$SH_DIR/update.sh" repo-status; pause ;;
            3)
                read -p "是否强制拉取？(y/N): " force
                if [[ $force == [yY] ]]; then
                    "$SH_DIR/update.sh" pull --force
                else
                    "$SH_DIR/update.sh" pull
                fi
                pause
                ;;
            4)
                read -p "是否强制从公有库拉取？(y/N): " force
                if [[ $force == [yY] ]]; then
                    "$SH_DIR/update.sh" pull-public --force
                else
                    "$SH_DIR/update.sh" pull-public
                fi
                pause
                ;;
            5)
                read -p "是否强制推送到私有库？(y/N): " force
                if [[ $force == [yY] ]]; then
                    "$SH_DIR/update.sh" push-private --force
                else
                    "$SH_DIR/update.sh" push-private
                fi
                pause
                ;;
            6) "$SH_DIR/update.sh" patch; pause ;;
            7) "$SH_DIR/update.sh" api-patch; pause ;;
            8) "$SH_DIR/gemini-patch.sh" apply; pause ;;
            9) "$SH_DIR/update.sh" verify; pause ;;
            10)
                read -p "是否强制更新？(y/N): " force
                if [[ $force == [yY] ]]; then
                    "$SH_DIR/update.sh" update --force
                else
                    "$SH_DIR/update.sh" update
                fi
                pause
                ;;
            11) "$SH_DIR/quick-fix.sh"; pause ;;
            12) "$SH_DIR/auto-update.sh"; pause ;;
            0) break ;;
            *) log_error "无效选择，请重新输入"; sleep 2 ;;
        esac
    done
}

# 监控菜单
monitor_menu() {
    while true; do
        clear
        log_header "========================================"
        log_header "           监控维护菜单"
        log_header "========================================"
        echo
        echo -e "${YELLOW}监控功能详细说明：${NC}"
        echo "  1) 完整系统监控    - 8项检查: 服务+API+资源+日志+性能 (生成评分报告)"
        echo "  2) 快速状态检查    - 仅检查关键服务: 主页+API+容器状态"
        echo "  3) 服务状态        - Docker容器详情+端口监听+连通性测试"
        echo "  4) 系统资源        - 内存/磁盘/CPU使用率+系统负载详情"
        echo "  5) 错误日志分析    - 最近1小时错误日志+统计分析"
        echo "  6) 网络连接测试    - 主页+API+数据库连接状态测试"
        echo "  0) 返回主菜单"
        echo
        read -p "请选择操作 [0-6]: " choice
        
        case $choice in
            1) "$SH_DIR/monitor.sh" monitor; pause ;;
            2) "$SH_DIR/monitor.sh" quick; pause ;;
            3) "$SH_DIR/service.sh" status; pause ;;
            4)
                echo "=== 系统资源使用情况 ==="
                echo "内存使用:"
                free -h
                echo
                echo "磁盘使用:"
                df -h /root/workspace/
                echo
                echo "CPU使用:"
                top -bn1 | head -5
                pause
                ;;
            5)
                echo "=== 错误日志分析 ==="
                echo "New-API 错误日志:"
                docker-compose -f "$NEW_API_DIR/docker-compose.yml" logs --since=1h new-api 2>/dev/null | grep -i error | tail -10 || echo "无错误日志"
                echo
                echo "Nginx 错误日志:"
                docker-compose -f "/root/workspace/shared/nginx/docker-compose.yml" logs --since=1h nginx-proxy 2>/dev/null | grep -E "(error|404|500)" | tail -10 || echo "无错误日志"
                pause
                ;;
            6)
                echo "=== 网络连接测试 ==="
                echo "测试主页访问:"
                if curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ai/ | grep -q "200"; then
                    log_success "✓ 主页访问正常"
                else
                    log_error "✗ 主页访问失败"
                fi

                echo "测试API状态:"
                if curl -k -s https://localhost/ai/api/status | grep -q "success"; then
                    log_success "✓ API状态正常"
                else
                    log_error "✗ API状态异常"
                fi

                echo "测试数据库连接:"
                if docker exec mysql mysql -uroot -p123456 -e "SELECT 1;" >/dev/null 2>&1; then
                    log_success "✓ 数据库连接正常"
                else
                    log_error "✗ 数据库连接失败"
                fi
                pause
                ;;
            0) break ;;
            *) log_error "无效选择，请重新输入"; sleep 2 ;;
        esac
    done
}

# 暂停函数
pause() {
    echo
    read -p "按回车键继续..."
}



# 定时备份设置
setup_auto_backup() {
    log_header "========================================"
    log_header "           定时备份设置"
    log_header "========================================"

    echo "当前定时任务："
    crontab -l 2>/dev/null | grep -E "(backup|database)" || echo "  未设置数据库定时备份"
    echo

    echo "选择备份频率："
    echo "  1) 每天凌晨2点备份   - 适合低频使用场景 (cron: 0 2 * * *)"
    echo "  2) 每12小时备份一次  - 适合中等频率使用 (cron: 0 */12 * * *)"
    echo "  3) 每6小时备份一次   - 适合高频使用场景 (cron: 0 */6 * * *)"
    echo "  4) 自定义时间        - 输入自定义cron表达式"
    echo "  5) 删除定时备份      - 移除所有自动备份任务"
    echo "  0) 返回"
    echo

    read -p "请选择 [0-5]: " backup_choice

    case $backup_choice in
        1)
            # 每天凌晨2点
            setup_cron_backup "0 2 * * *" "每天凌晨2点"
            ;;
        2)
            # 每12小时
            setup_cron_backup "0 */12 * * *" "每12小时"
            ;;
        3)
            # 每6小时
            setup_cron_backup "0 */6 * * *" "每6小时"
            ;;
        4)
            echo "请输入cron表达式（例如：0 2 * * * 表示每天凌晨2点）："
            read -p "cron表达式: " custom_cron
            read -p "描述: " custom_desc
            setup_cron_backup "$custom_cron" "$custom_desc"
            ;;
        5)
            remove_cron_backup
            ;;
        0)
            return
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 设置定时备份
setup_cron_backup() {
    local cron_time="$1"
    local description="$2"

    # 创建自动备份脚本
    create_auto_backup_script

    # 移除旧的备份任务
    crontab -l 2>/dev/null | grep -v "database backup" | crontab -

    # 添加新的备份任务
    (crontab -l 2>/dev/null; echo "$cron_time $CRON_BACKUP_SCRIPT # New-API database backup") | crontab -

    log_success "定时备份已设置：$description"
    log_info "备份脚本：$CRON_BACKUP_SCRIPT"
}

# 移除定时备份
remove_cron_backup() {
    crontab -l 2>/dev/null | grep -v "database backup" | crontab -
    log_success "定时备份已移除"
}

# 快速操作函数
quick_start() {
    log_info "快速启动 New-API..."
    "$SH_DIR/service.sh" start
}

quick_stop() {
    log_info "快速停止 New-API..."
    "$SH_DIR/service.sh" stop
}

quick_restart() {
    log_info "快速重启 New-API..."
    "$SH_DIR/service.sh" restart
}

quick_status() {
    log_info "快速状态检查..."
    "$SH_DIR/monitor.sh" quick
}

quick_backup() {
    log_info "快速数据库备份..."
    "$SH_DIR/database.sh" backup
}

# Git 操作函数
repo_status() {
    log_info "显示仓库状态..."
    cd "$NEW_API_DIR"

    echo "========================================"
    echo "Git 仓库状态"
    echo "========================================"

    # 当前分支和提交
    local current_branch=$(git branch --show-current)
    local current_commit=$(git rev-parse --short HEAD)
    echo "当前分支: $current_branch"
    echo "当前提交: $current_commit"
    echo

    # 远程仓库
    echo "远程仓库:"
    git remote -v
    echo

    # 工作区状态
    echo "工作区状态:"
    if [ -n "$(git status --porcelain)" ]; then
        git status --short
    else
        echo "  工作区干净"
    fi
}

pull_from_public() {
    log_info "从公有库拉取最新代码..."
    cd "$NEW_API_DIR"

    # 检查是否已添加公有库远程仓库
    if ! git remote | grep -q "public"; then
        log_info "添加公有库远程仓库..."
        git remote add public "https://github.com/Calcium-Ion/new-api.git"
    fi

    # 获取公有库最新代码
    log_info "获取公有库最新更新..."
    if git fetch public; then
        log_success "公有库代码获取成功"

        # 检查是否有新的更新
        local updates=$(git log --oneline HEAD..public/main 2>/dev/null | wc -l)
        if [ "$updates" -eq 0 ]; then
            log_info "公有库没有新的更新"
            return 0
        fi

        log_info "发现公有库 $updates 个新提交"
        read -p "是否从公有库合并最新代码？(y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            if git merge public/main; then
                log_success "公有库代码合并成功"
            else
                log_error "公有库代码合并失败，可能存在冲突"
            fi
        fi
    else
        log_error "公有库代码获取失败"
    fi
}

push_to_private() {
    log_info "推送代码到私有库..."
    cd "$NEW_API_DIR"

    # 检查是否有未提交的更改
    if [ -n "$(git status --porcelain)" ]; then
        log_warning "检测到未提交的更改:"
        git status --short

        read -p "是否提交这些更改？(y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            read -p "请输入提交信息: " commit_msg
            if [ -z "$commit_msg" ]; then
                commit_msg="Auto commit $(date '+%Y-%m-%d %H:%M:%S')"
            fi

            git add .
            git commit -m "$commit_msg"
            log_success "更改已提交"
        else
            log_error "存在未提交的更改，无法推送"
            return 1
        fi
    fi

    # 推送到私有库（origin）
    local current_branch=$(git branch --show-current)
    log_info "推送分支 '$current_branch' 到私有库..."

    if git push origin "$current_branch"; then
        log_success "代码推送到私有库成功"
        local current_commit=$(git rev-parse --short HEAD)
        log_info "推送的提交: $current_commit"
    else
        log_error "代码推送失败"
        read -p "是否强制推送？(y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            if git push origin "$current_branch" --force; then
                log_success "强制推送成功"
                log_warning "注意：强制推送会覆盖远程历史记录"
            else
                log_error "强制推送失败"
            fi
        fi
    fi
}

# 创建自动备份脚本
create_auto_backup_script() {
    log_info "创建自动备份脚本..."

    cat > "$CRON_BACKUP_SCRIPT" << 'EOF'
#!/bin/bash

# New-API 自动备份脚本
# 由管理脚本自动生成

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"; }

# 配置
SCRIPT_DIR="/root/workspace/new-api/sh"
LOG_FILE="/root/workspace/new-api/auto-backup.log"

# 记录日志
exec >> "$LOG_FILE" 2>&1

log_info "开始自动备份..."

# 检查数据库服务
if ! docker ps | grep -q mysql; then
    log_error "MySQL 容器未运行，跳过备份"
    exit 1
fi

# 执行备份（自动保留最新3个备份）
if "$SCRIPT_DIR/database.sh" backup "auto_$(date +%Y%m%d_%H%M%S)"; then
    log_success "自动备份完成，已自动保留最新3个备份"
else
    log_error "自动备份失败"
    exit 1
fi
EOF

    chmod +x "$CRON_BACKUP_SCRIPT"
    log_success "自动备份脚本创建完成"
}

# 显示帮助信息
show_help() {
    clear
    log_header "========================================"
    log_header "        New-API 管理脚本帮助"
    log_header "========================================"
    echo
    echo -e "${CYAN}基本用法:${NC}"
    echo "  $0                      # 启动交互式菜单"
    echo "  $0 <命令>               # 直接执行命令"
    echo
    echo -e "${CYAN}快速命令:${NC}"
    echo "  start                   # 启动服务"
    echo "  stop                    # 停止服务"
    echo "  restart                 # 重启服务"
    echo "  status                  # 状态检查"
    echo "  backup                  # 数据库备份"
    echo
    echo -e "${CYAN}主要功能:${NC}"
    echo "  服务管理                # 启动、停止、重启服务"
    echo "  代码管理                # Git 仓库操作和代码同步"
    echo "  数据库管理              # 备份、恢复、维护数据库"
    echo "  定时备份                # 设置数据库自动备份计划"
    echo
    echo -e "${CYAN}子脚本说明:${NC}"
    echo "  sh/service.sh           # 服务管理"
    echo "  sh/database.sh          # 数据库管理"
    echo
    echo -e "${CYAN}配置文件:${NC}"
    echo "  new-api错误指南.md      # 错误解决指南"
    echo "  new-api更新指导.md      # 更新操作指导"
    echo
    pause
}

# 主函数
main() {
    # 检查脚本环境
    if ! check_scripts; then
        log_error "脚本环境检查失败"
        exit 1
    fi
    
    # 处理命令行参数
    case "${1:-menu}" in
        "start") quick_start ;;
        "stop") quick_stop ;;
        "restart") quick_restart ;;
        "status") quick_status ;;
        "backup") quick_backup ;;
        "help") show_help ;;
        "menu"|"")
            # 交互式菜单
            while true; do
                show_main_menu
                read -p "请选择操作 [0-13，回车退出]: " choice

                # 处理空输入（回车）默认退出
                if [ -z "$choice" ]; then
                    choice="0"
                fi

                case $choice in
                    1) "$SH_DIR/service.sh" start; pause ;;
                    2) "$SH_DIR/service.sh" stop; pause ;;
                    3) "$SH_DIR/service.sh" restart; pause ;;
                    4) "$SH_DIR/service.sh" status; pause ;;
                    5) service_menu ;;
                    6) repo_status; pause ;;
                    7) pull_from_public; pause ;;
                    8) push_to_private; pause ;;
                    9) "$SH_DIR/database.sh" backup; pause ;;
                    10) database_menu ;;
                    11) "$SH_DIR/database.sh" maintain; pause ;;
                    12) setup_auto_backup; pause ;;
                    13) show_help ;;
                    0)
                        log_success "感谢使用 New-API 管理系统！"
                        exit 0
                        ;;
                    *)
                        log_error "无效选择，请重新输入"
                        sleep 2
                        ;;
                esac
            done
            ;;
        *)
            log_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
